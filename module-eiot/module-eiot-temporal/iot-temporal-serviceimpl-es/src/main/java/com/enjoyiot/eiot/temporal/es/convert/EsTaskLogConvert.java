
/*
 *
 *  * | Licensed 未经许可不能去掉「Enjoy-iot」相关版权
 *  * +----------------------------------------------------------------------
 *  * | Author: <EMAIL> | Tel: 19918996474
 *  * +----------------------------------------------------------------------
 *
 *  Copyright [2025] [Enjoy-iot] | Tel: 19918996474
 *
 *  Licensed under the Apache License, Version 2.0 (the "License");
 *  you may not use this file except in compliance with the License.
 *  You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 *  Unless required by applicable law or agreed to in writing, software
 *  distributed under the License is distributed on an "AS IS" BASIS,
 *  WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 *  See the License for the specific language governing permissions and
 *  limitations under the License.
 * /
 */
package com.enjoyiot.eiot.temporal.es.convert;



import com.enjoyiot.eiot.temporal.es.document.DocRuleLog;
import com.enjoyiot.eiot.temporal.es.document.DocTaskLog;
import com.enjoyiot.module.eiot.api.rule.dto.RuleLog;
import com.enjoyiot.module.eiot.api.task.dto.TaskLog;
import org.mapstruct.Builder;
import org.mapstruct.Mapper;
import org.mapstruct.factory.Mappers;

/**
 * @Author: EnjoyIot
 * @Date: 2024/12/24 19:02
 * @Version: V1.0
 * @Description:
 */
@Mapper(builder = @Builder(disableBuilder = true))

public interface EsTaskLogConvert {
    EsTaskLogConvert INSTANCE = Mappers.getMapper(EsTaskLogConvert.class);

    TaskLog convert(DocTaskLog o);

    DocTaskLog convertDoc(TaskLog log);
}

