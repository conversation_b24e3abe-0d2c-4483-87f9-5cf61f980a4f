<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.enjoy-iot</groupId>
        <artifactId>module-eiot-temporal</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>iot-temporal-service</artifactId>
    <description>时序数据库接口</description>
    <dependencies>
        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>iot-common-thing</artifactId>
        </dependency>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>module-eiot-api</artifactId>
        </dependency>

    </dependencies>


</project>
