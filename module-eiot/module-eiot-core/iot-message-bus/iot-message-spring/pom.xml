<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.enjoy-iot</groupId>
        <artifactId>iot-message-bus</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>iot-message-spring</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>iot-message-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>common</artifactId>
        </dependency>


        <!--====================第三方库===================-->

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-autoconfigure</artifactId>
        </dependency>

        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-configuration-processor</artifactId>
            <optional>true</optional>
        </dependency>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>iot-common-thing</artifactId>
        </dependency>


    </dependencies>
</project>
