<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.enjoy-iot</groupId>
        <artifactId>iot-components</artifactId>
        <version>${revision}</version>
    </parent>

    <artifactId>iot-component-coap</artifactId>

    <dependencies>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>iot-component-core</artifactId>
        </dependency>

        <dependency>
            <groupId>com.enjoy-iot</groupId>
            <artifactId>module-eiot-api</artifactId>
        </dependency>

        <dependency>
            <groupId>io.vertx</groupId>
            <artifactId>vertx-web</artifactId>
        </dependency>


        <dependency>
            <groupId>org.eclipse.californium</groupId>
            <artifactId>californium-core</artifactId>
            <version>2.0.0-M7</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.californium</groupId>
            <artifactId>element-connector</artifactId>
            <version>2.0.0-M7</version>
        </dependency>
        <dependency>
            <groupId>org.eclipse.californium</groupId>
            <artifactId>scandium</artifactId>
            <version>2.0.0-M7</version>
        </dependency>





    </dependencies>

</project>
