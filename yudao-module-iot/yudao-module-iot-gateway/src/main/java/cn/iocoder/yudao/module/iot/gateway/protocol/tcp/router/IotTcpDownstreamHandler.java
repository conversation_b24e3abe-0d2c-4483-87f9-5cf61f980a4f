package cn.iocoder.yudao.module.iot.gateway.protocol.tcp.router;

import cn.iocoder.yudao.module.iot.core.mq.message.IotDeviceMessage;
import cn.iocoder.yudao.module.iot.gateway.codec.tcp.IotTcpCodecManager;
import cn.iocoder.yudao.module.iot.gateway.service.device.message.IotDeviceMessageService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * IoT 网关 TCP 下行消息处理器
 * <p>
 * 负责处理从业务系统发送到设备的下行消息，包括：
 * 1. 属性设置
 * 2. 服务调用
 * 3. 属性获取
 * 4. 配置下发
 * 5. OTA 升级
 * <p>
 * 注意：由于移除了连接管理器，此处理器主要负责消息的编码和日志记录
 *
 * <AUTHOR>
 */
@Slf4j
@Component
public class IotTcpDownstreamHandler {

    private final IotDeviceMessageService messageService;
    private final IotTcpCodecManager codecManager;

    public IotTcpDownstreamHandler(IotDeviceMessageService messageService, IotTcpCodecManager codecManager) {
        this.messageService = messageService;
        this.codecManager = codecManager;
    }

    /**
     * 处理下行消息
     *
     * @param message 设备消息
     */
    public void handle(IotDeviceMessage message) {
        try {
            log.info("[handle][处理下行消息] 设备 ID: {}, 方法: {}, 消息 ID: {}",
                    message.getDeviceId(), message.getMethod(), message.getId());

            // 编码消息用于日志记录和验证
            byte[] encodedMessage = codecManager.encode(message);
            log.debug("[handle][消息编码成功] 设备 ID: {}, 编码后长度: {} 字节",
                    message.getDeviceId(), encodedMessage.length);

            // 记录下行消息处理
            log.info("[handle][下行消息处理完成] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams());

        } catch (Exception e) {
            log.error("[handle][处理下行消息失败] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams(), e);
        }
    }

    /**
     * 使用 JSON 协议处理下行消息
     *
     * @param message 设备消息
     */
    public void handleWithJsonProtocol(IotDeviceMessage message) {
        try {
            log.info("[handleWithJsonProtocol][使用 JSON 协议处理下行消息] 设备 ID: {}, 方法: {}, 消息 ID: {}",
                    message.getDeviceId(), message.getMethod(), message.getId());

            // 使用 JSON 协议编码消息
            byte[] encodedMessage = codecManager.encodeJson(message);
            log.debug("[handleWithJsonProtocol][JSON 消息编码成功] 设备 ID: {}, 编码后长度: {} 字节",
                    message.getDeviceId(), encodedMessage.length);

            // 记录下行消息处理
            log.info("[handleWithJsonProtocol][JSON 下行消息处理完成] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams());

        } catch (Exception e) {
            log.error("[handleWithJsonProtocol][JSON 协议处理下行消息失败] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams(), e);
        }
    }

    /**
     * 使用二进制协议处理下行消息
     *
     * @param message 设备消息
     */
    public void handleWithBinaryProtocol(IotDeviceMessage message) {
        try {
            log.info("[handleWithBinaryProtocol][使用二进制协议处理下行消息] 设备 ID: {}, 方法: {}, 消息 ID: {}",
                    message.getDeviceId(), message.getMethod(), message.getId());

            // 使用二进制协议编码消息
            byte[] encodedMessage = codecManager.encodeBinary(message);
            log.debug("[handleWithBinaryProtocol][二进制消息编码成功] 设备 ID: {}, 编码后长度: {} 字节",
                    message.getDeviceId(), encodedMessage.length);

            // 记录下行消息处理
            log.info("[handleWithBinaryProtocol][二进制下行消息处理完成] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams());

        } catch (Exception e) {
            log.error("[handleWithBinaryProtocol][二进制协议处理下行消息失败] 设备 ID: {}, 方法: {}, 消息内容: {}",
                    message.getDeviceId(), message.getMethod(), message.getParams(), e);
        }
    }

    /**
     * 设置协议类型
     *
     * @param useJson 是否使用 JSON 协议
     */
    public void setProtocol(boolean useJson) {
        if (useJson) {
            codecManager.useJsonProtocol();
            log.info("[setProtocol][设置使用 JSON 协议]");
        } else {
            codecManager.useBinaryProtocol();
            log.info("[setProtocol][设置使用二进制协议]");
        }
    }

    /**
     * 获取当前协议类型
     *
     * @return 当前协议类型
     */
    public String getCurrentProtocol() {
        return codecManager.getCurrentProtocol().name();
    }
}