package cn.iocoder.yudao.module.iot.gateway.protocol.tcp.router;

import cn.hutool.core.util.IdUtil;
import cn.iocoder.yudao.module.iot.core.biz.dto.IotDeviceRespDTO;
import cn.iocoder.yudao.module.iot.core.mq.message.IotDeviceMessage;
import cn.iocoder.yudao.module.iot.gateway.codec.tcp.IotTcpCodecManager;
import cn.iocoder.yudao.module.iot.gateway.protocol.tcp.IotTcpUpstreamProtocol;
import cn.iocoder.yudao.module.iot.gateway.service.device.IotDeviceService;
import cn.iocoder.yudao.module.iot.gateway.service.device.message.IotDeviceMessageService;
import io.vertx.core.Handler;
import io.vertx.core.buffer.Buffer;
import io.vertx.core.net.NetSocket;
import lombok.extern.slf4j.Slf4j;

/**
 * IoT 网关 TCP 上行消息处理器
 *
 * 负责处理设备通过 TCP 连接发送的上行消息，包括：
 * 1. 数据上报
 * 2. 心跳消息
 * 3. 事件上报
 * 4. 设备注册
 *
 * <AUTHOR>
 */
@Slf4j
public class IotTcpUpstreamHandler implements Handler<NetSocket> {

    private final IotDeviceMessageService deviceMessageService;
    private final IotDeviceService deviceService;
    private final String serverId;
    private final IotTcpCodecManager codecManager;

    /**
     * 最大数据包大小（1MB）
     */
    private static final int MAX_PACKET_SIZE = 1024 * 1024;

    public IotTcpUpstreamHandler(IotTcpUpstreamProtocol protocol, IotDeviceMessageService deviceMessageService,
            IotDeviceService deviceService, IotTcpCodecManager codecManager) {
        this.deviceMessageService = deviceMessageService;
        this.deviceService = deviceService;
        this.serverId = protocol.getServerId();
        this.codecManager = codecManager;
    }

    @Override
    public void handle(NetSocket socket) {
        // 生成客户端 ID 用于日志标识
        String clientId = IdUtil.simpleUUID();
        log.info("[handle][收到设备连接] 客户端 ID: {}, 地址: {}", clientId, socket.remoteAddress());

        try {
            // 设置异常处理
            socket.exceptionHandler(ex -> {
                log.error("[handle][连接异常] 客户端 ID: {}, 地址: {}", clientId, socket.remoteAddress(), ex);
            });

            // 设置连接关闭处理
            socket.closeHandler(v -> {
                log.info("[handle][连接关闭] 客户端 ID: {}, 地址: {}", clientId, socket.remoteAddress());
            });

            // 设置数据处理器 - 使用原始数据处理器，不使用固定长度解析器
            socket.handler(buffer -> {
                try {
                    handleDataPackage(clientId, buffer);
                } catch (Exception e) {
                    log.error("[handle][处理数据包异常] 客户端 ID: {}", clientId, e);
                }
            });

        } catch (Exception e) {
            log.error("[handle][设置连接处理器失败] 客户端 ID: {}, 地址: {}", clientId, socket.remoteAddress(), e);
            socket.close();
        }
    }

    /**
     * 处理数据包
     *
     * @param clientId 客户端 ID
     * @param buffer   数据缓冲区
     */
    private void handleDataPackage(String clientId, Buffer buffer) {
        try {
            // 记录原始数据
            log.debug("[handleDataPackage][收到原始数据] 客户端 ID: {}, 数据长度: {} 字节, 数据: {}",
                    clientId, buffer.length(), buffer.toString());

            // 校验数据包大小
            if (buffer.length() > MAX_PACKET_SIZE) {
                log.warn("[handleDataPackage][数据包过大] 客户端 ID: {}, 数据包大小: {} 字节, 最大允许: {} 字节",
                        clientId, buffer.length(), MAX_PACKET_SIZE);
                return;
            }

            // 检查数据包是否为空
            if (buffer.length() == 0) {
                log.warn("[handleDataPackage][数据包为空] 客户端 ID: {}", clientId);
                return;
            }

            // 使用编解码器管理器自动检测协议并解码消息
            IotDeviceMessage message = codecManager.decode(buffer.getBytes());

            // 校验消息
            if (!validateMessage(message)) {
                log.warn("[handleDataPackage][消息校验失败] 客户端 ID: {}, 消息 ID: {}", clientId, message.getId());
                return;
            }

            log.info("[handleDataPackage][接收数据包] 客户端 ID: {}, 方法: {}, 设备 ID: {}",
                    clientId, message.getMethod(), message.getDeviceId());

            // 处理上行消息
            handleUpstreamMessage(clientId, message);

        } catch (Exception e) {
            log.error("[handleDataPackage][处理数据包失败] 客户端 ID: {}, 数据包大小: {} 字节, 错误: {}",
                    clientId, buffer.length(), e.getMessage(), e);

            // 尝试解析原始数据以便调试
            try {
                String rawData = buffer.toString();
                log.error("[handleDataPackage][原始数据内容] 客户端 ID: {}, 数据: {}", clientId, rawData);
            } catch (Exception ex) {
                log.error("[handleDataPackage][无法解析原始数据] 客户端 ID: {}", clientId, ex);
            }
        }
    }

    /**
     * 处理上行消息
     *
     * @param clientId 客户端 ID
     * @param message  设备消息
     */
    private void handleUpstreamMessage(String clientId, IotDeviceMessage message) {
        try {
            log.info("[handleUpstreamMessage][上行消息] 客户端 ID: {}, 方法: {}, 设备 ID: {}",
                    clientId, message.getMethod(), message.getDeviceId());

            // 根据设备 ID 查询设备信息
            IotDeviceRespDTO device = getDeviceInfo(message.getDeviceId());
            if (device == null) {
                log.error("[handleUpstreamMessage][设备不存在] 客户端 ID: {}, 设备 ID: {}", clientId, message.getDeviceId());
                return;
            }

            // 发送消息到队列
            deviceMessageService.sendDeviceMessage(message, device.getProductKey(), device.getDeviceName(), serverId);

            log.debug("[handleUpstreamMessage][上行消息处理完成] 客户端 ID: {}, 消息 ID: {}, 设备 ID: {}, 产品: {}, 设备: {}",
                    clientId, message.getId(), message.getDeviceId(), device.getProductKey(), device.getDeviceName());

        } catch (Exception e) {
            log.error("[handleUpstreamMessage][处理上行消息失败] 客户端 ID: {}, 消息 ID: {}, 设备 ID: {}",
                    clientId, message.getId(), message.getDeviceId(), e);
        }
    }

    /**
     * 根据设备 ID 获取设备信息
     *
     * @param deviceId 设备 ID
     * @return 设备信息，如果设备不存在则返回 null
     */
    private IotDeviceRespDTO getDeviceInfo(Long deviceId) {
        try {
            if (deviceId == null) {
                log.warn("[getDeviceInfo][设备 ID 为空]");
                return null;
            }

            // 从缓存中查询设备信息
            IotDeviceRespDTO device = deviceService.getDeviceFromCache(deviceId);

            if (device == null) {
                log.warn("[getDeviceInfo][设备不存在] 设备 ID: {}", deviceId);
                return null;
            }

            log.debug("[getDeviceInfo][获取设备信息成功] 设备 ID: {}, 产品: {}, 设备: {}",
                    deviceId, device.getProductKey(), device.getDeviceName());

            return device;

        } catch (Exception e) {
            log.error("[getDeviceInfo][查询设备信息失败] 设备 ID: {}", deviceId, e);
            return null;
        }
    }

    /**
     * 校验消息
     *
     * @param message 设备消息
     * @return 是否有效
     */
    private boolean validateMessage(IotDeviceMessage message) {
        if (message == null) {
            log.warn("[validateMessage][消息为空]");
            return false;
        }

        // 校验消息方法
        String method = message.getMethod();
        if (method == null || method.trim().isEmpty()) {
            log.warn("[validateMessage][消息方法为空] 消息 ID: {}", message.getId());
            return false;
        }

        // 校验设备 ID
        Long deviceId = message.getDeviceId();
        if (deviceId == null) {
            log.warn("[validateMessage][设备 ID 为空] 消息 ID: {}, 方法: {}", message.getId(), method);
            return false;
        }

        // 校验消息 ID
        String messageId = message.getId();
        if (messageId == null || messageId.trim().isEmpty()) {
            log.warn("[validateMessage][消息 ID 为空] 设备 ID: {}, 方法: {}", deviceId, method);
            return false;
        }

        return true;
    }
}